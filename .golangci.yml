linters:
  enable:
    - depguard
    - goimports
    - misspell
    - revive

issues:
  exclude-rules:
    - path: _test.go
      linters:
        - errcheck

linters-settings:
  depguard:
    rules:
      no_exec_policy:
        files:
          - "!$test"
        deny:
          - pkg: "os/exec"
            desc: "Using os/exec to run sub processes it not allowed by policy"
  errcheck:
    exclude-functions:
      # Used in HTTP handlers, any error is handled by the server itself.
      - (net/http.ResponseWriter).Write
  revive:
    rules:
      # https://github.com/mgechev/revive/blob/master/RULES_DESCRIPTIONS.md#unused-parameter
      - name: unused-parameter
        severity: warning
        disabled: true

package collector

import (
	"context"
	"log/slog"
	"os"
	"strings"
	"sync"
	"time"

	"github.com/prometheus/client_golang/prometheus"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/rest"
)

func init() {
	registerCollector("gpuhour", defaultEnabled, NewGPUTotalHoursCollector)
}

var (
	GpucardUpdateDuration  = 5 * time.Minute
	GpucardCleanDuration   = GpucardUpdateDuration * 4
	GpucardExpiredDuration = GpucardUpdateDuration * 3
	UserNameSpace          = "hero-user"
	MatchedResourceName    = "nvidia.com"
	GPUHOUR                = "gpuhour"
)

type Metric struct {
	Buffer      []prometheus.Metric
	ExpiredTime time.Time
}

type MetricsData struct {
	Metrics map[int64]*Metric
	mtx     sync.Mutex
}

type ResourceStats struct {
	ResourceCount int64
	PodGpuHour    float64
}

type NodeResourceStats struct {
	Pods         map[string]*ResourceStats
	NodeGpuHour  float64
	ResourceName string
}

type GPUTotalHoursCollector struct {
	logger    *slog.Logger
	clientset *kubernetes.Clientset

	cache      MetricsData
	cacheMutex sync.RWMutex

	desc         *prometheus.Desc
	subsystem    string
	podGpuStats  NodeResourceStats
	nodeName     string
	resourceName string

	collectTicker *time.Ticker
	cleanupTicker *time.Ticker
}

func NewGPUTotalHoursCollector(logger *slog.Logger) (Collector, error) {
	config, err := rest.InClusterConfig()
	if err != nil {
		logger.Error("Error building kubeconfig", "err", err)
		return nil, err
	}
	K8sClientSet := kubernetes.NewForConfigOrDie(config)

	g := &GPUTotalHoursCollector{
		clientset: K8sClientSet,
		desc: prometheus.NewDesc(
			"leinao_node_gpu_time_total",
			"node gpu hour total",
			[]string{"node", "resource"}, nil,
		),
		subsystem:     "gpuhour",
		podGpuStats:   NodeResourceStats{Pods: make(map[string]*ResourceStats)},
		collectTicker: time.NewTicker(GpucardUpdateDuration),
		cleanupTicker: time.NewTicker(GpucardCleanDuration),
		cache:         MetricsData{Metrics: make(map[int64]*Metric)},
		logger:        logger,
	}

	nodeName := os.Getenv("NODE_NAME")
	if nodeName == "" {
		if nodeName, err = os.Hostname(); err != nil {
			return nil, err
		}
	}
	g.startBackgroundTasks()
	return g, nil
}

func (g *GPUTotalHoursCollector) getNodeResourceName() string {
	node, err := g.clientset.CoreV1().Nodes().Get(context.TODO(), g.nodeName, metav1.GetOptions{})
	if err != nil {
		g.logger.Error("Error getting node status %s: %v", g.nodeName, err)
	}
	for resourceName, quantity := range node.Status.Allocatable {
		if strings.Contains(resourceName.String(), MatchedResourceName) && quantity.Value() > 0 {
			return resourceName.String()
		}
	}
	return "nocard"
}

func (g *GPUTotalHoursCollector) Update(ch chan<- prometheus.Metric) error {
	if cachedMetrics := g.getCachedMetrics(); len(cachedMetrics) > 0 {
		for _, metric := range cachedMetrics {
			ch <- metric
		}
	}
	return nil
}

func (g *GPUTotalHoursCollector) getCachedMetrics() []prometheus.Metric {
	g.cacheMutex.RLock()
	defer g.cacheMutex.RUnlock()

	if len(g.cache.Metrics) == 0 {
		return nil
	}

	var result []prometheus.Metric
	for _, data := range g.cache.Metrics {
		result = append(result, data.Buffer...)
	}

	// Clear cache after reading
	g.cache.Metrics = make(map[int64]*Metric)
	return result
}

func (g *GPUTotalHoursCollector) startBackgroundTasks() error {

	// Cleanup goroutine
	go func() {
		for tc := range g.cleanupTicker.C {
			g.cleanCache(tc)
		}
	}()
	go func() {
		for tc := range g.collectTicker.C {
			go g.collectAndCacheMetrics(tc)
		}
	}()

	return nil
}

func (g *GPUTotalHoursCollector) collectAndCacheMetrics(tc time.Time) {
	listOptions := metav1.ListOptions{
		FieldSelector: "spec.nodeName=" + g.nodeName,
	}

	pods, err := g.clientset.CoreV1().Pods(UserNameSpace).List(context.TODO(), listOptions)
	if err != nil {
		g.logger.Error(err.Error())
	}
	g.getGPUCountByNode(pods.Items, GpucardUpdateDuration.Hours())
	g.syncNodeTimeHour()

	if g.podGpuStats.ResourceName == "" {
		g.podGpuStats.ResourceName = g.getNodeResourceName()
	}
	g.cacheMutex.Lock()
	defer g.cacheMutex.Unlock()

	tempMetric := &Metric{ExpiredTime: tc.Add(GpucardExpiredDuration)}
	tempMetric.Buffer = append(tempMetric.Buffer,
		prometheus.NewMetricWithTimestamp(tc, prometheus.MustNewConstMetric(g.desc, prometheus.CounterValue, g.podGpuStats.NodeGpuHour, g.nodeName, g.podGpuStats.ResourceName)))
	g.cache.Metrics[tc.Unix()] = tempMetric
}

func (g *GPUTotalHoursCollector) cleanCache(tc time.Time) {
	g.cacheMutex.Lock()
	defer g.cacheMutex.Unlock()

	var toDelete []int64
	for timestamp, metric := range g.cache.Metrics {
		if metric.ExpiredTime.Before(tc) {
			toDelete = append(toDelete, timestamp)
		}
	}

	for _, key := range toDelete {
		delete(g.cache.Metrics, key)
	}
}

func (g *GPUTotalHoursCollector) syncNodeTimeHour() {
	for _, podStats := range g.podGpuStats.Pods {
		g.podGpuStats.NodeGpuHour += podStats.PodGpuHour
	}
}

func (g *GPUTotalHoursCollector) getGPUCountByNode(pods []corev1.Pod, duration float64) {
	for _, pod := range pods {
		switch pod.Status.Phase {
		case corev1.PodRunning:
			for _, container := range pod.Spec.Containers {
				for resourceName, quantity := range container.Resources.Requests {
					if strings.Contains(resourceName.String(), MatchedResourceName) {
						if _, exists := g.podGpuStats.Pods[pod.Name]; exists {
							g.podGpuStats.Pods[pod.Name].PodGpuHour = duration * float64(quantity.Value())
						} else {
							g.podGpuStats.ResourceName = string(resourceName)
							g.podGpuStats.Pods[pod.Name] = &ResourceStats{
								ResourceCount: quantity.Value(),
							}
						}

					}
				}
			}
		case corev1.PodFailed:
			if _, exists := g.podGpuStats.Pods[pod.Name]; exists {
				delete(g.podGpuStats.Pods, pod.Name)
			}
		case corev1.PodPending:

		}
	}

}

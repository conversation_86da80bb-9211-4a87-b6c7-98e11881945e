# HELP node_textfile_mtime_seconds Unixtime mtime of textfiles successfully read.
# TYPE node_textfile_mtime_seconds gauge
node_textfile_mtime_seconds{file="fixtures/textfile/two_metric_files/metrics1.prom"} 1
node_textfile_mtime_seconds{file="fixtures/textfile/two_metric_files/metrics2.prom"} 1
# HELP node_textfile_scrape_error 1 if there was an error opening or reading a file, 0 otherwise
# TYPE node_textfile_scrape_error gauge
node_textfile_scrape_error 0
# HELP testmetric1_1 Metric read from fixtures/textfile/two_metric_files/metrics1.prom
# TYPE testmetric1_1 untyped
testmetric1_1{foo="bar"} 10
# HELP testmetric1_2 Metric read from fixtures/textfile/two_metric_files/metrics1.prom
# TYPE testmetric1_2 untyped
testmetric1_2{foo="baz"} 20
# HELP testmetric2_1 Metric read from fixtures/textfile/two_metric_files/metrics2.prom
# TYPE testmetric2_1 untyped
testmetric2_1{foo="bar"} 30
# HELP testmetric2_2 Metric read from fixtures/textfile/two_metric_files/metrics2.prom
# TYPE testmetric2_2 untyped
testmetric2_2{foo="baz"} 40

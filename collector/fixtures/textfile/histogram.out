# HELP node_textfile_mtime_seconds Unixtime mtime of textfiles successfully read.
# TYPE node_textfile_mtime_seconds gauge
node_textfile_mtime_seconds{file="fixtures/textfile/histogram/metrics.prom"} 1
# HELP node_textfile_scrape_error 1 if there was an error opening or reading a file, 0 otherwise
# TYPE node_textfile_scrape_error gauge
node_textfile_scrape_error 0
# HELP prometheus_tsdb_compaction_chunk_range Final time range of chunks on their first compaction
# TYPE prometheus_tsdb_compaction_chunk_range histogram
prometheus_tsdb_compaction_chunk_range_bucket{le="100"} 0
prometheus_tsdb_compaction_chunk_range_bucket{le="400"} 0
prometheus_tsdb_compaction_chunk_range_bucket{le="1600"} 0
prometheus_tsdb_compaction_chunk_range_bucket{le="6400"} 0
prometheus_tsdb_compaction_chunk_range_bucket{le="25600"} 7
prometheus_tsdb_compaction_chunk_range_bucket{le="102400"} 7
prometheus_tsdb_compaction_chunk_range_bucket{le="409600"} 1.412839e+06
prometheus_tsdb_compaction_chunk_range_bucket{le="1.6384e+06"} 1.69185e+06
prometheus_tsdb_compaction_chunk_range_bucket{le="6.5536e+06"} 1.691853e+06
prometheus_tsdb_compaction_chunk_range_bucket{le="2.62144e+07"} 1.691853e+06
prometheus_tsdb_compaction_chunk_range_bucket{le="+Inf"} 1.691853e+06
prometheus_tsdb_compaction_chunk_range_sum 6.71393432189e+11
prometheus_tsdb_compaction_chunk_range_count 1.691853e+06

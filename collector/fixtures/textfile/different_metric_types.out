# HELP event_duration_seconds_total Query timings
# TYPE event_duration_seconds_total summary
event_duration_seconds_total{baz="inner_eval",quantile="0.5"} 1.073e-06
event_duration_seconds_total{baz="inner_eval",quantile="0.9"} 1.928e-06
event_duration_seconds_total{baz="inner_eval",quantile="0.99"} 4.35e-06
event_duration_seconds_total_sum{baz="inner_eval"} 1.8652166505091474e+06
event_duration_seconds_total_count{baz="inner_eval"} 1.492355615e+09
event_duration_seconds_total{baz="prepare_time",quantile="0.5"} 4.283e-06
event_duration_seconds_total{baz="prepare_time",quantile="0.9"} 7.796e-06
event_duration_seconds_total{baz="prepare_time",quantile="0.99"} 2.2083e-05
event_duration_seconds_total_sum{baz="prepare_time"} 840923.7919437207
event_duration_seconds_total_count{baz="prepare_time"} 1.492355814e+09
event_duration_seconds_total{baz="result_append",quantile="0.5"} 1.566e-06
event_duration_seconds_total{baz="result_append",quantile="0.9"} 3.223e-06
event_duration_seconds_total{baz="result_append",quantile="0.99"} 6.53e-06
event_duration_seconds_total_sum{baz="result_append"} 4.404109951000078
event_duration_seconds_total_count{baz="result_append"} 1.427647e+06
event_duration_seconds_total{baz="result_sort",quantile="0.5"} 1.847e-06
event_duration_seconds_total{baz="result_sort",quantile="0.9"} 2.975e-06
event_duration_seconds_total{baz="result_sort",quantile="0.99"} 4.08e-06
event_duration_seconds_total_sum{baz="result_sort"} 3.4123187829998307
event_duration_seconds_total_count{baz="result_sort"} 1.427647e+06
# HELP events_total this is a test metric
# TYPE events_total counter
events_total{foo="bar"} 10
events_total{foo="baz"} 20
# HELP node_textfile_mtime_seconds Unixtime mtime of textfiles successfully read.
# TYPE node_textfile_mtime_seconds gauge
node_textfile_mtime_seconds{file="fixtures/textfile/different_metric_types/metrics.prom"} 1
# HELP node_textfile_scrape_error 1 if there was an error opening or reading a file, 0 otherwise
# TYPE node_textfile_scrape_error gauge
node_textfile_scrape_error 0

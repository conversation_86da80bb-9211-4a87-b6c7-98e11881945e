// Copyright 2017-2019 The Prometheus Authors
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
// http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

//go:build !noinfiniband
// +build !noinfiniband

package collector

import (
	"context"
	"errors"
	"fmt"
	"log/slog"
	"os"
	"regexp"
	"strconv"
	"strings"
	"sync"
	"sync/atomic"
	"time"

	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/procfs/sysfs"

	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/informers"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/rest"
	"k8s.io/client-go/tools/cache"
)

const (
	defaultKubeConfigPath = "/root/.kube/config"
	monitoringLabelKey    = "leinao.ai/second-monitor"
	monitoringLabelValue  = "true"
	rdmaResourcePattern   = `^rdma/hca`
	updateInterval        = 1 * time.Second
	cleanupInterval       = 60 * time.Second
	expiredDuration       = 60 * time.Second
	resyncPeriod          = 30 * time.Second
	userNamespace         = "hero-user"

	defaultBufferSize = 256
	maxCacheEntries   = 60

	envNodeName   = "NODE_NAME"
	envKubeConfig = "KUBECONFIG"
)

// MetricData represents cached metric data
type MetricData struct {
	Buffer      []prometheus.Metric
	ExpiredTime time.Time
}

// Simplified pool
var metricDataPool = sync.Pool{
	New: func() interface{} {
		return &MetricData{
			Buffer: make([]prometheus.Metric, 0, defaultBufferSize),
		}
	},
}

type infinibandCollector struct {
	fs          sysfs.FS
	metricDescs map[string]*prometheus.Desc
	logger      *slog.Logger
	subsystem   string

	// Kubernetes
	clientset       kubernetes.Interface
	informerFactory informers.SharedInformerFactory
	podInformer     cache.SharedIndexInformer
	stopCh          chan struct{}

	// Cache and state
	cache        map[int64]*MetricData
	cacheMutex   sync.RWMutex
	highFreqMode int32

	// Background tasks
	ctx           context.Context
	cancel        context.CancelFunc
	collectTicker *time.Ticker
	cleanupTicker *time.Ticker
}

func init() {
	registerCollector("infiniband", defaultEnabled, NewInfiniBandCollector)
}

func NewInfiniBandCollector(logger *slog.Logger) (Collector, error) {
	fs, err := sysfs.NewFS(*sysPath)
	if err != nil {
		return nil, fmt.Errorf("failed to open sysfs: %w", err)
	}

	ctx, cancel := context.WithCancel(context.Background())

	c := &infinibandCollector{
		fs:        fs,
		logger:    logger,
		cache:     make(map[int64]*MetricData, maxCacheEntries),
		stopCh:    make(chan struct{}),
		ctx:       ctx,
		cancel:    cancel,
		subsystem: "infiniband",
	}

	if err := c.initKubernetesClient(); err != nil {
		logger.Warn("failed to initialize Kubernetes client, using standard mode", "error", err)
	}

	c.initMetricDescs()
	c.startBackgroundTasks()

	return c, nil
}

func (c *infinibandCollector) initKubernetesClient() error {
	// kubeConfigPath := os.Getenv(envKubeConfig)
	// if kubeConfigPath == "" {
	// 	kubeConfigPath = defaultKubeConfigPath
	// }

	// config, err := clientcmd.BuildConfigFromFlags("", kubeConfigPath)
	config, err := rest.InClusterConfig()
	if err != nil {
		return fmt.Errorf("failed to create config: %w", err)
	}

	c.clientset, err = kubernetes.NewForConfig(config)
	if err != nil {
		return fmt.Errorf("failed to create clientset: %w", err)
	}

	nodeName := os.Getenv(envNodeName)
	if nodeName == "" {
		if nodeName, err = os.Hostname(); err != nil {
			return fmt.Errorf("failed to get hostname: %w", err)
		}
	}
	c.logger.Info(fmt.Sprintf("collect nodeName: %s", strings.ToLower(nodeName)))
	c.informerFactory = informers.NewSharedInformerFactoryWithOptions(
		c.clientset, resyncPeriod,
		informers.WithTweakListOptions(func(options *metav1.ListOptions) {
			options.FieldSelector = fmt.Sprintf(
				"spec.nodeName=%s,status.phase=Running,metadata.namespace=%s",
				strings.ToLower(nodeName), // 将 nodeName 值转为小写
				userNamespace,
			)
		}),
	)

	c.podInformer = c.informerFactory.Core().V1().Pods().Informer()
	c.podInformer.AddEventHandler(cache.ResourceEventHandlerFuncs{
		AddFunc:    c.onPodEvent,
		UpdateFunc: func(_, newObj interface{}) { c.onPodEvent(newObj) },
		DeleteFunc: func(interface{}) { c.updateMonitoringMode() },
	})

	c.informerFactory.Start(c.stopCh)
	go func() {
		if !cache.WaitForCacheSync(c.stopCh, c.podInformer.HasSynced) {
			c.logger.Error("failed to sync pod cache")
		}
	}()

	return nil
}

func (c *infinibandCollector) onPodEvent(obj interface{}) {
	pod := obj.(*v1.Pod)
	if c.hasRdmaResourceRequest(pod) {
		if value, exists := pod.Labels[monitoringLabelKey]; exists && value == monitoringLabelValue {
			c.setHighFrequencyMode(true)
			return
		}
	}
	c.updateMonitoringMode()
}

func (c *infinibandCollector) hasRdmaResourceRequest(pod *v1.Pod) bool {
	rdmaRegex := regexp.MustCompile(rdmaResourcePattern)

	// 检查所有容器的资源请求
	for _, container := range pod.Spec.Containers {
		for resourceName := range container.Resources.Requests {
			if rdmaRegex.MatchString(string(resourceName)) {
				return true
			}
		}
	}
	return false
}

func (c *infinibandCollector) updateMonitoringMode() {
	if c.podInformer == nil {
		return
	}

	hasMonitoringPod := false
	for _, obj := range c.podInformer.GetStore().List() {
		pod := obj.(*v1.Pod)
		if pod.Status.Phase == v1.PodRunning && c.hasRdmaResourceRequest(pod) {
			if value, exists := pod.Labels[monitoringLabelKey]; exists && value == monitoringLabelValue {
				hasMonitoringPod = true
				break
			}
		}
	}
	c.setHighFrequencyMode(hasMonitoringPod)
}

func (c *infinibandCollector) setHighFrequencyMode(enable bool) {
	mode := int32(0)
	if enable {
		mode = 1
	}

	if atomic.SwapInt32(&c.highFreqMode, mode) == mode {
		return // No change
	}

	if c.collectTicker != nil {
		c.collectTicker.Stop()
		c.collectTicker = nil
	}

	if enable {
		c.collectTicker = time.NewTicker(updateInterval)
		c.logger.Info("enabled high frequency mode")
	} else {
		c.logger.Info("disabled high frequency mode")
	}
}

func (c *infinibandCollector) isHighFrequencyMode() bool {
	return atomic.LoadInt32(&c.highFreqMode) == 1
}

func (c *infinibandCollector) startBackgroundTasks() {
	c.cleanupTicker = time.NewTicker(cleanupInterval)

	// Cleanup goroutine
	go func() {
		defer c.cleanupTicker.Stop()
		for {
			select {
			case <-c.ctx.Done():
				return
			case tc := <-c.cleanupTicker.C:
				c.cleanCache(tc)
			}
		}
	}()

	// Collection goroutine
	go func() {
		for {
			select {
			case <-c.ctx.Done():
				return
			default:
				if c.isHighFrequencyMode() && c.collectTicker != nil {
					select {
					case tc := <-c.collectTicker.C:
						go c.collectAndCacheMetrics(tc)
					case <-c.ctx.Done():
						return
					default:
						time.Sleep(100 * time.Millisecond)
					}
				} else {
					time.Sleep(time.Second)
				}
			}
		}
	}()
}

func (c *infinibandCollector) collectAndCacheMetrics(tc time.Time) {
	metricData := metricDataPool.Get().(*MetricData)
	defer func() {
		metricData.Buffer = metricData.Buffer[:0]
		metricDataPool.Put(metricData)
	}()

	metricData.ExpiredTime = tc.Add(expiredDuration)

	devices, err := c.fs.InfiniBandClass()
	if err != nil {
		if !errors.Is(err, os.ErrNotExist) {
			c.logger.Error("error obtaining InfiniBand class info", "error", err)
		}
		return
	}

	for _, device := range devices {
		c.addDeviceMetrics(tc, metricData, device)
	}

	c.cacheMutex.Lock()
	if len(c.cache) >= maxCacheEntries {
		c.evictOldestEntry()
	}

	// Clone buffer to avoid pool interference
	cachedData := &MetricData{
		Buffer:      make([]prometheus.Metric, len(metricData.Buffer)),
		ExpiredTime: metricData.ExpiredTime,
	}
	copy(cachedData.Buffer, metricData.Buffer)
	c.cache[tc.Unix()] = cachedData
	c.cacheMutex.Unlock()
}

func (c *infinibandCollector) addDeviceMetrics(tc time.Time, metricData *MetricData, device sysfs.InfiniBandDevice) {
	// Device info metric
	infoDesc := prometheus.NewDesc(
		prometheus.BuildFQName(namespace, c.subsystem, "info"),
		"Non-numeric data from /sys/class/infiniband/<device>, value is always 1.",
		[]string{"device", "board_id", "firmware_version", "hca_type"}, nil,
	)
	metricData.Buffer = append(metricData.Buffer,
		prometheus.NewMetricWithTimestamp(tc,
			prometheus.MustNewConstMetric(infoDesc, prometheus.GaugeValue, 1.0,
				device.Name, device.BoardID, device.FirmwareVersion, device.HCAType)))

	for _, port := range device.Ports {
		c.addPortMetrics(tc, metricData, device.Name, port)
	}
}

func (c *infinibandCollector) addPortMetrics(tc time.Time, metricData *MetricData, deviceName string, port sysfs.InfiniBandPort) {
	portStr := strconv.FormatUint(uint64(port.Port), 10)

	// Gauge metrics
	gauges := []struct {
		name  string
		value float64
	}{
		{"state_id", float64(port.StateID)},
		{"physical_state_id", float64(port.PhysStateID)},
		{"rate_bytes_per_second", float64(port.Rate)},
	}

	for _, g := range gauges {
		metricData.Buffer = append(metricData.Buffer,
			prometheus.NewMetricWithTimestamp(tc,
				prometheus.MustNewConstMetric(c.metricDescs[g.name], prometheus.GaugeValue, g.value, deviceName, portStr)))
	}

	// Counter metrics
	c.addCounterMetrics(tc, metricData, deviceName, portStr, port)
}

func (c *infinibandCollector) addCounterMetrics(tc time.Time, metricData *MetricData, deviceName, portStr string, port sysfs.InfiniBandPort) {
	counters := []struct {
		name  string
		value *uint64
	}{
		{"legacy_multicast_packets_received_total", port.Counters.LegacyPortMulticastRcvPackets},
		{"legacy_multicast_packets_transmitted_total", port.Counters.LegacyPortMulticastXmitPackets},
		{"legacy_data_received_bytes_total", port.Counters.LegacyPortRcvData64},
		{"legacy_packets_received_total", port.Counters.LegacyPortRcvPackets64},
		{"legacy_unicast_packets_received_total", port.Counters.LegacyPortUnicastRcvPackets},
		{"legacy_unicast_packets_transmitted_total", port.Counters.LegacyPortUnicastXmitPackets},
		{"legacy_data_transmitted_bytes_total", port.Counters.LegacyPortXmitData64},
		{"legacy_packets_transmitted_total", port.Counters.LegacyPortXmitPackets64},
		{"excessive_buffer_overrun_errors_total", port.Counters.ExcessiveBufferOverrunErrors},
		{"link_downed_total", port.Counters.LinkDowned},
		{"link_error_recovery_total", port.Counters.LinkErrorRecovery},
		{"local_link_integrity_errors_total", port.Counters.LocalLinkIntegrityErrors},
		{"multicast_packets_received_total", port.Counters.MulticastRcvPackets},
		{"multicast_packets_transmitted_total", port.Counters.MulticastXmitPackets},
		{"port_constraint_errors_received_total", port.Counters.PortRcvConstraintErrors},
		{"port_constraint_errors_transmitted_total", port.Counters.PortXmitConstraintErrors},
		{"port_data_received_bytes_total", port.Counters.PortRcvData},
		{"port_data_transmitted_bytes_total", port.Counters.PortXmitData},
		{"port_discards_received_total", port.Counters.PortRcvDiscards},
		{"port_discards_transmitted_total", port.Counters.PortXmitDiscards},
		{"port_errors_received_total", port.Counters.PortRcvErrors},
		{"port_packets_received_total", port.Counters.PortRcvPackets},
		{"port_packets_transmitted_total", port.Counters.PortXmitPackets},
		{"port_transmit_wait_total", port.Counters.PortXmitWait},
		{"unicast_packets_received_total", port.Counters.UnicastRcvPackets},
		{"unicast_packets_transmitted_total", port.Counters.UnicastXmitPackets},
		{"port_receive_remote_physical_errors_total", port.Counters.PortRcvRemotePhysicalErrors},
		{"port_receive_switch_relay_errors_total", port.Counters.PortRcvSwitchRelayErrors},
		{"symbol_error_total", port.Counters.SymbolError},
		{"vl15_dropped_total", port.Counters.VL15Dropped},
	}

	for _, counter := range counters {
		if counter.value != nil {
			metricData.Buffer = append(metricData.Buffer,
				prometheus.NewMetricWithTimestamp(tc,
					prometheus.MustNewConstMetric(c.metricDescs[counter.name], prometheus.CounterValue, float64(*counter.value), deviceName, portStr)))
		}
	}

	// Hardware counters
	hwCounters := []struct {
		name  string
		value *uint64
	}{
		{"hw_np_cnp_sent", port.HwCounters.NpCnpSent},
		{"hw_np_ecn_marked_roce_packets", port.HwCounters.NpEcnMarkedRocePackets},
		{"hw_rp_cnp_handled", port.HwCounters.RpCnpHandled},
		{"hw_rp_cnp_ignored", port.HwCounters.RpCnpIgnored},
		{"hw_out_of_buffer", port.HwCounters.OutOfBuffer},
		{"hw_out_of_sequence", port.HwCounters.OutOfSequence},
	}

	for _, hwCounter := range hwCounters {
		if hwCounter.value != nil {
			metricData.Buffer = append(metricData.Buffer,
				prometheus.NewMetricWithTimestamp(tc,
					prometheus.MustNewConstMetric(c.metricDescs[hwCounter.name], prometheus.CounterValue, float64(*hwCounter.value), deviceName, portStr)))
		}
	}
}

func (c *infinibandCollector) evictOldestEntry() {
	var oldestKey int64
	var oldestTime time.Time
	first := true

	for key, data := range c.cache {
		if first || data.ExpiredTime.Before(oldestTime) {
			oldestKey = key
			oldestTime = data.ExpiredTime
			first = false
		}
	}

	if !first {
		delete(c.cache, oldestKey)
	}
}

func (c *infinibandCollector) cleanCache(tc time.Time) {
	c.cacheMutex.Lock()
	defer c.cacheMutex.Unlock()

	var toDelete []int64
	for timestamp, metric := range c.cache {
		if metric.ExpiredTime.Before(tc) {
			toDelete = append(toDelete, timestamp)
		}
	}

	for _, key := range toDelete {
		delete(c.cache, key)
	}
}

func (c *infinibandCollector) getCachedMetrics() []prometheus.Metric {
	c.cacheMutex.Lock()
	defer c.cacheMutex.Unlock()

	if len(c.cache) == 0 {
		return nil
	}

	var result []prometheus.Metric
	for _, data := range c.cache {
		result = append(result, data.Buffer...)
	}

	// Clear cache after reading
	c.cache = make(map[int64]*MetricData, maxCacheEntries)
	return result
}

func (c *infinibandCollector) collectMetrics(ch chan<- prometheus.Metric) error {
	devices, err := c.fs.InfiniBandClass()
	if err != nil {
		if errors.Is(err, os.ErrNotExist) {
			return ErrNoData
		}
		return fmt.Errorf("error obtaining InfiniBand class info: %w", err)
	}

	for _, device := range devices {
		// Device info
		infoDesc := prometheus.NewDesc(
			prometheus.BuildFQName(namespace, c.subsystem, "info"),
			"Non-numeric data from /sys/class/infiniband/<device>, value is always 1.",
			[]string{"device", "board_id", "firmware_version", "hca_type"}, nil,
		)
		ch <- prometheus.MustNewConstMetric(infoDesc, prometheus.GaugeValue, 1.0,
			device.Name, device.BoardID, device.FirmwareVersion, device.HCAType)

		for _, port := range device.Ports {
			portStr := strconv.FormatUint(uint64(port.Port), 10)

			// Gauge metrics
			ch <- prometheus.MustNewConstMetric(c.metricDescs["state_id"], prometheus.GaugeValue, float64(port.StateID), device.Name, portStr)
			ch <- prometheus.MustNewConstMetric(c.metricDescs["physical_state_id"], prometheus.GaugeValue, float64(port.PhysStateID), device.Name, portStr)
			ch <- prometheus.MustNewConstMetric(c.metricDescs["rate_bytes_per_second"], prometheus.GaugeValue, float64(port.Rate), device.Name, portStr)

			// Counter metrics - only essential ones for direct collection
			c.addDirectCounters(ch, device.Name, portStr, port)
		}
	}

	return nil
}

func (c *infinibandCollector) addDirectCounters(ch chan<- prometheus.Metric, deviceName, portStr string, port sysfs.InfiniBandPort) {
	counters := []struct {
		name  string
		value *uint64
	}{
		{"port_data_received_bytes_total", port.Counters.PortRcvData},
		{"port_data_transmitted_bytes_total", port.Counters.PortXmitData},
		{"port_packets_received_total", port.Counters.PortRcvPackets},
		{"port_packets_transmitted_total", port.Counters.PortXmitPackets},
		{"port_errors_received_total", port.Counters.PortRcvErrors},
	}

	for _, counter := range counters {
		if counter.value != nil {
			ch <- prometheus.MustNewConstMetric(c.metricDescs[counter.name], prometheus.CounterValue, float64(*counter.value), deviceName, portStr)
		}
	}
}

func (c *infinibandCollector) Update(ch chan<- prometheus.Metric) error {
	if c.isHighFrequencyMode() {
		if cachedMetrics := c.getCachedMetrics(); len(cachedMetrics) > 0 {
			for _, metric := range cachedMetrics {
				ch <- metric
			}
			return nil
		}
	}
	return c.collectMetrics(ch)
}

func (c *infinibandCollector) initMetricDescs() {
	descriptions := map[string]string{
		"hw_np_cnp_sent":                             "Number of cnp packets send",
		"hw_np_ecn_marked_roce_packets":              "Number of ecn marked packets received",
		"hw_out_of_buffer":                           "out of buffer",
		"hw_out_of_sequence":                         "out_of_sequence",
		"hw_rp_cnp_handled":                          "Number of cnp packets handled",
		"hw_rp_cnp_ignored":                          "Number of cnp packets ignored",
		"legacy_multicast_packets_received_total":    "Number of multicast packets received",
		"legacy_multicast_packets_transmitted_total": "Number of multicast packets transmitted",
		"legacy_data_received_bytes_total":           "Number of data octets received on all links",
		"legacy_packets_received_total":              "Number of data packets received on all links",
		"legacy_unicast_packets_received_total":      "Number of unicast packets received",
		"legacy_unicast_packets_transmitted_total":   "Number of unicast packets transmitted",
		"legacy_data_transmitted_bytes_total":        "Number of data octets transmitted on all links",
		"legacy_packets_transmitted_total":           "Number of data packets received on all links",
		"excessive_buffer_overrun_errors_total":      "Number of times that OverrunErrors consecutive flow control update periods occurred, each having at least one overrun error.",
		"link_downed_total":                          "Number of times the link failed to recover from an error state and went down",
		"link_error_recovery_total":                  "Number of times the link successfully recovered from an error state",
		"local_link_integrity_errors_total":          "Number of times that the count of local physical errors exceeded the threshold specified by LocalPhyErrors.",
		"multicast_packets_received_total":           "Number of multicast packets received (including errors)",
		"multicast_packets_transmitted_total":        "Number of multicast packets transmitted (including errors)",
		"physical_state_id":                          "Physical state of the InfiniBand port (0: no change, 1: sleep, 2: polling, 3: disable, 4: shift, 5: link up, 6: link error recover, 7: phytest)",
		"port_constraint_errors_received_total":      "Number of packets received on the switch physical port that are discarded",
		"port_constraint_errors_transmitted_total":   "Number of packets not transmitted from the switch physical port",
		"port_data_received_bytes_total":             "Number of data octets received on all links",
		"port_data_transmitted_bytes_total":          "Number of data octets transmitted on all links",
		"port_discards_received_total":               "Number of inbound packets discarded by the port because the port is down or congested",
		"port_discards_transmitted_total":            "Number of outbound packets discarded by the port because the port is down or congested",
		"port_errors_received_total":                 "Number of packets containing an error that were received on this port",
		"port_packets_received_total":                "Number of packets received on all VLs by this port (including errors)",
		"port_packets_transmitted_total":             "Number of packets transmitted on all VLs from this port (including errors)",
		"port_transmit_wait_total":                   "Number of ticks during which the port had data to transmit but no data was sent during the entire tick",
		"rate_bytes_per_second":                      "Maximum signal transfer rate",
		"state_id":                                   "State of the InfiniBand port (0: no change, 1: down, 2: init, 3: armed, 4: active, 5: act defer)",
		"unicast_packets_received_total":             "Number of unicast packets received (including errors)",
		"unicast_packets_transmitted_total":          "Number of unicast packets transmitted (including errors)",
		"port_receive_remote_physical_errors_total":  "Number of packets marked with the EBP (End of Bad Packet) delimiter received on the port.",
		"port_receive_switch_relay_errors_total":     "Number of packets that could not be forwarded by the switch.",
		"symbol_error_total":                         "Number of minor link errors detected on one or more physical lanes.",
		"vl15_dropped_total":                         "Number of incoming VL15 packets dropped due to resource limitations.",
	}

	c.metricDescs = make(map[string]*prometheus.Desc, len(descriptions))
	for metricName, description := range descriptions {
		c.metricDescs[metricName] = prometheus.NewDesc(
			prometheus.BuildFQName(namespace, c.subsystem, metricName),
			description,
			[]string{"device", "port"},
			nil,
		)
	}
}

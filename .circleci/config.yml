---
version: 2.1
orbs:
  prometheus: prometheus/prometheus@0.17.1
executors:
  # Whenever the Go version is updated here, .promu.yml and .promu-cgo.yml
  # should also be updated.
  golang:
    docker:
      - image: cimg/go:1.23
  arm:
    docker:
      - image: cimg/go:1.23
    resource_class: arm.medium

jobs:
  test:
    executor: golang
    steps:
      - prometheus/setup_environment
      - run: go mod download
      - run: make
      - prometheus/store_artifact:
          file: node_exporter
  test-arm:
    executor: arm
    steps:
      - prometheus/setup_environment
      - run: uname -a
      - run: make test-e2e
  test_mixins:
    executor: golang
    steps:
      - checkout
      - run: go install github.com/google/go-jsonnet/cmd/jsonnet@latest
      - run: go install github.com/google/go-jsonnet/cmd/jsonnetfmt@latest
      - run: go install github.com/jsonnet-bundler/jsonnet-bundler/cmd/jb@latest
      - run: make promtool
      - run: make -C docs/node-mixin clean
      - run: make -C docs/node-mixin jb_install
      - run: make -C docs/node-mixin
      - run: git diff --exit-code
  build:
    machine:
      image: ubuntu-2204:current
    parallelism: 3
    steps:
      - prometheus/setup_environment
      - run: docker run --privileged linuxkit/binfmt:af88a591f9cc896a52ce596b9cf7ca26a061ef97
      - run: promu crossbuild -v --parallelism $CIRCLE_NODE_TOTAL --parallelism-thread $CIRCLE_NODE_INDEX
      - run: promu --config .promu-cgo.yml crossbuild -v --parallelism $CIRCLE_NODE_TOTAL --parallelism-thread $CIRCLE_NODE_INDEX
      # sign the darwin build so it doesn't get SIGKILLed on start, see: https://github.com/prometheus/node_exporter/issues/2539
      - run:
          command: |
            if [[ -f "$(pwd)/.build/darwin-arm64/node_exporter" ]]; then
                promu codesign "$(pwd)/.build/darwin-arm64/node_exporter"
            fi

            if [[ -f "$(pwd)/.build/darwin-amd64/node_exporter" ]]; then
                promu codesign "$(pwd)/.build/darwin-amd64/node_exporter"                
            fi
      - persist_to_workspace:
          root: .
          paths:
            - .build
      - store_artifacts:
          path: .build
          destination: /build
  test_docker:
    machine:
      image: ubuntu-2204:current
    environment:
      DOCKER_TEST_IMAGE_NAME: quay.io/prometheus/golang-builder:1.23-base
      REPO_PATH: github.com/prometheus/node_exporter
    steps:
      - prometheus/setup_environment
      - attach_workspace:
          at: .
      - run:
          command: |
            if [ -n "$CIRCLE_TAG" ]; then
              make docker DOCKER_IMAGE_TAG=$CIRCLE_TAG
            else
              make docker
            fi
      - run: docker images
      - run: docker run --rm -t -v "$(pwd):/app" "${DOCKER_TEST_IMAGE_NAME}" -i "${REPO_PATH}" -T
      - run:
          command: |
            if [ -n "$CIRCLE_TAG" ]; then
              make test-docker DOCKER_IMAGE_TAG=$CIRCLE_TAG
            else
              make test-docker
            fi
workflows:
  version: 2
  node_exporter:
    jobs:
      - test:
          filters:
            tags:
              only: /.*/
      - test-arm:
          filters:
            tags:
              only: /.*/
      - build:
          filters:
            tags:
              only: /.*/
      - test_docker:
          requires:
            - test
            - build
          filters:
            tags:
              only: /.*/
      - test_mixins:
          filters:
            tags:
              only: /.*/
      - prometheus/publish_master:
          context: org-context
          requires:
            - test
            - build
          filters:
            branches:
              only: master
      - prometheus/publish_release:
          context: org-context
          requires:
            - test
            - build
          filters:
            tags:
              only: /^v.*/
            branches:
              ignore: /.*/

# Compiled Object files, Static and Dynamic libs (Shared Objects)
*.o
*.a
*.so

# Folders
_obj
_test

# Architecture specific extensions/prefixes
*.[568vq]
[568vq].out

*.cgo1.go
*.cgo2.c
_cgo_defun.c
_cgo_gotypes.go
_cgo_export.*

_testmain.go

*.exe
dependencies-stamp
/node_exporter
/.build
/.deps
/.release
/.tarballs

# Intellij

/.idea
*.iml

# Test files extracted from ttar
collector/fixtures/sys/
collector/fixtures/udev/

/vendor

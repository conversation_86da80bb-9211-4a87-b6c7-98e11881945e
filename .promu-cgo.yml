go:
    # Whenever the Go version is updated here, .circle/config.yml and
    # .promu.yml should also be updated.
    version: 1.23
    cgo: true
repository:
    path: github.com/prometheus/node_exporter
build:
    binaries:
        - name: node_exporter
    ldflags: |
        -X github.com/prometheus/common/version.Version={{.Version}}
        -X github.com/prometheus/common/version.Revision={{.Revision}}
        -X github.com/prometheus/common/version.Branch={{.Branch}}
        -X github.com/prometheus/common/version.BuildUser={{user}}@{{host}}
        -X github.com/prometheus/common/version.BuildDate={{date "20060102-15:04:05"}}
tarball:
    files:
        - LICENSE
        - NOTICE
crossbuild:
    platforms:
        - darwin/amd64
        - darwin/arm64
        - netbsd/amd64
        - netbsd/386
